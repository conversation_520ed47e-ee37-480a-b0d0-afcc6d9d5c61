// Board and Task Related Types

import type { DraggableProvidedDraggableProps, DraggableProvidedDragHandleProps } from "@hello-pangea/dnd";
import type { Column } from "../redux/slice/boardSlice";
import type { CSSProperties, Ref } from "react";

export interface ColumnProps {
  column: Column;
}

export interface TaskProps {
  title: string;
  description: string;
  id: string;
  columnId: string;
  dragHandleProps?: DraggableProvidedDragHandleProps | null;
  draggableProps?: DraggableProvidedDraggableProps | null;
  innerRef?: Ref<HTMLDivElement>;
  style?: CSSProperties;
}

export interface DragEndParams {
  source: {
    droppableId: string;
    index: number;
  };
  destination: {
    droppableId: string;
    index: number;
  };
}

// Re-export types from Redux slice for convenience
export type { Task, Column } from "../redux/slice/boardSlice";
