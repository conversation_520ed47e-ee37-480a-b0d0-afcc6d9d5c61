// Board and Task Related Types

import type { Column, Task } from "../redux/slice/boardSlice";

export interface ColumnProps {
  column: Column;
}

export interface TaskProps {
  title: string;
  description: string;
  id: string;
  columnId: string;
}

export interface DragEndParams {
  source: {
    droppableId: string;
    index: number;
  };
  destination: {
    droppableId: string;
    index: number;
  };
}

// Re-export types from Redux slice for convenience
export type { Task, Column } from "../redux/slice/boardSlice";
