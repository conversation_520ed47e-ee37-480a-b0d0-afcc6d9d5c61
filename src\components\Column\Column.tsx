import AddTask from "../AddTask/AddTask";
import Task from "../Task/Task";
import ColumnTitle from "../ui/Titles/ColumnTitle";
import type { ColumnProps } from "../../types/types";
import { v4 as uuidv4 } from 'uuid';
import { useDispatch } from "react-redux";
import { addTask } from "../../redux/slice/boardSlice";
import {
  Droppable,
  Draggable,
} from '@hello-pangea/dnd';

export default function Column({column} : ColumnProps) {
  const tasks = column.tasks;
  const dispatch = useDispatch();

  const handleAddTask = (taskTitle: string) => {
    const newTask = {
      id: uuidv4(),
      title: taskTitle,
      description: "",
    };
    dispatch(addTask({ columnId: column.id, task: newTask }));
  };

  return (
    <div className="overflow-y-auto flex flex-col h-min shadow-sm gap-2 p-2.5 bg-white/90 sm:w-[300px] rounded-sm">
      <div className="flex items-center justify-between mb-2">
        <ColumnTitle columnTitle={column.name} />
        <span className="text-sm text-gray-500 font-medium">{tasks.length} {tasks.length === 1 ? 'task' : 'tasks'}</span>
      </div>

      {/* منطقة إسقاط المهام */}
      <Droppable droppableId={column.id} type="task">
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`flex flex-col gap-2 min-h-[100px] transition-colors ${
              snapshot.isDraggingOver ? 'bg-blue-50 rounded-md' : ''
            }`}
          >
            {tasks.map((task, index) => (
              <Draggable draggableId={task.id} index={index} key={task.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className={`transition-transform ${
                      snapshot.isDragging ? 'rotate-2 scale-105 shadow-lg' : ''
                    }`}
                  >
                    <Task
                      id={task.id}
                      columnId={column.id}
                      description={task.description}
                      title={task.title}
                    />
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>

      <AddTask onTaskAdded={handleAddTask}/>
    </div>
  )
}
