import AddTask from "../AddTask/AddTask";
import Task from "../Task/Task";
import ColumnTitle from "../ui/Titles/ColumnTitle";
import type { ColumnProps } from "../../types/types";
import { v4 as uuidv4 } from 'uuid';
import { useDispatch } from "react-redux";
import { addTask } from "../../redux/slice/boardSlice";

export default function Column({column} : ColumnProps) {
  const tasks = column.tasks;
  const dispatch = useDispatch();

  const handleAddTask = (taskTitle: string) => {
    const newTask = {
      id: uuidv4(),
      title: taskTitle,
      description: "", 
    };
    dispatch(addTask({ columnId: column.id, task: newTask }));
  };
  return (
    <div className=" overflow-y-auto flex flex-col h-min shadow-sm gap-2 p-2.5 bg-white/90 sm:w-[300px] rounded-sm ">
      <div className="flex items-center justify-between mb-2">
        <ColumnTitle columnTitle={column.name} />
        <span className="text-sm text-gray-500 font-medium">{tasks.length} {tasks.length === 1 ? 'task' : 'tasks'}</span>
      </div>
      {tasks.map((task) => ( 
        <Task id={task.id} columnId={column.id} key={task.id} description = {task.description} title={task.title} />) )}
      <AddTask onTaskAdded={handleAddTask}/>
    </div>
  )
}
