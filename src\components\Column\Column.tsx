import AddTask from "../AddTask/AddTask";
import Task from "../Task/Task";
import ColumnTitle from "../ui/Titles/ColumnTitle";
import type { ColumnProps } from "../../types/types";
import { v4 as uuidv4 } from 'uuid';
import { useDispatch } from "react-redux";
import { addTask } from "../../redux/slice/boardSlice";
import {
  Droppable,
  Draggable,
} from '@hello-pangea/dnd';

export default function Column({column} : ColumnProps) {
  const tasks = column.tasks;
  const dispatch = useDispatch();

  const handleAddTask = (taskTitle: string) => {
    const newTask = {
      id: uuidv4(),
      title: taskTitle,
      description: "",
    };
    dispatch(addTask({ columnId: column.id, task: newTask }));
  };

  return (
    <div className="flex flex-col shadow-sm bg-white/90 sm:w-[300px] rounded-sm min-h-[400px] max-h-[80vh]">
      <div className="flex items-center justify-between mb-2 p-2.5 pb-0">
        <ColumnTitle columnTitle={column.name} />
        <span className="text-sm text-gray-500 font-medium">{tasks.length} {tasks.length === 1 ? 'task' : 'tasks'}</span>
      </div>

      {/* منطقة إسقاط المهام */}
      <Droppable droppableId={column.id} type="task">
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`flex flex-col gap-2 flex-1 min-h-[200px] px-2.5 transition-all duration-200 ${
              snapshot.isDraggingOver
                ? 'bg-blue-100 border-2 border-dashed border-blue-400 rounded-md mx-1'
                : 'border-2 border-transparent'
            }`}
            style={{
              backgroundColor: snapshot.isDraggingOver ? '#eff6ff' : 'transparent',
            }}
          >
            {tasks.map((task, index) => (
              <Draggable draggableId={task.id} index={index} key={task.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className={`transition-all duration-200 ${
                      snapshot.isDragging
                        ? 'rotate-2 scale-105 shadow-xl z-50 opacity-90'
                        : 'hover:shadow-md'
                    }`}
                    style={{
                      ...provided.draggableProps.style,
                      transform: snapshot.isDragging
                        ? `${provided.draggableProps.style?.transform} rotate(2deg)`
                        : provided.draggableProps.style?.transform,
                    }}
                  >
                    <Task
                      id={task.id}
                      columnId={column.id}
                      description={task.description}
                      title={task.title}
                    />
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}

            {/* رسالة عندما يكون العمود فارغ */}
            {tasks.length === 0 && !snapshot.isDraggingOver && (
              <div className="flex items-center justify-center h-32 text-gray-400 text-sm">
                اسحب المهام هنا
              </div>
            )}
          </div>
        )}
      </Droppable>

      <div className="p-2.5 pt-0">
        <AddTask onTaskAdded={handleAddTask}/>
      </div>
    </div>
  )
}
