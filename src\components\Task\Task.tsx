import { useState, memo } from "react";
import type { TaskProps } from "@/types/board.types";
import TaskHeader from "./TaskHeader";
import TaskContent from "./TaskContent";

const Task = memo(function Task({
  title,
  description,
  id,
  columnId,
}: TaskProps) {
  const [show, setShow] = useState(false);

  const onToggle = () => {
    setShow((prev) => !prev);
  };

  return (
    <div
      className="flex w-full flex-col gap-2.5 border border-gray-200 dark:border-slate-600 shadow-sm rounded-lg p-3 bg-white dark:bg-slate-800 cursor-grab active:cursor-grabbing hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-md transition-all duration-150 select-none"
    >
      <TaskHeader title={title} show={show} onToggle={onToggle} />
      {show && (
        <TaskContent
          title={title}
          description={description}
          columnId={columnId}
          taskId={id}
        />
      )}
    </div>
  );
});

export default Task;
