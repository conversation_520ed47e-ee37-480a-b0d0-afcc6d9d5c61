import { useState, memo } from "react";
import type { TaskProps } from "@/types/board.types";
import TaskHeader from "./TaskHeader";
import TaskContent from "./TaskContent";

const Task = memo(function Task({
  title,
  description,
  id,
  columnId,
  dragHandleProps,
  draggableProps,
  innerRef,
  style,
}: TaskProps) {
  const [show, setShow] = useState(false);
  const onToggle = () => {
    setShow((prev) => !prev);
  };

  return (
    <div
      {...draggableProps}
      {...dragHandleProps}
      ref={innerRef}
      style={style}
      className="flex w-full flex-col gap-2.5 border border-gray-200 shadow-sm rounded-sm p-2 bg-white cursor-grab active:cursor-grabbing hover:shadow-md hover:border-blue-300 transition-shadow duration-150 select-none"
    >
      <TaskHeader title={title} show={show} onToggle={onToggle} />
      {show && (
        <TaskContent
          title={title}
          description={description}
          columnId={columnId}
          taskId={id}
        />
      )}
    </div>
  );
});

export default Task;
