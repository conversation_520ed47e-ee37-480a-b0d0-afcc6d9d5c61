export interface TextareaProps {
  editDescription: string;
  setEditDescription: (value: string) => void;
}

export default function Textarea({editDescription , setEditDescription} : TextareaProps) {
  return (
    <textarea value={editDescription} onChange={(e) => setEditDescription(e.target.value)} placeholder="Task Description" rows={4} className="border border-gray-300 rounded px-3 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500" />
  )
}
