import { useState } from "react";
import { Form, AddNew } from "../ui";
import type { AddItemProps } from "../../types/ui.types";

export default function AddItem({ placeholder, label, onAdd, className }: AddItemProps) {
  const [showForm , setShowForm] = useState(false);
  
  const toggleForm  = () => 
  {
    setShowForm(prev => !prev)
  }
    const handleSubmit = (value: string): void => {
    if (!value.trim()) return; 

    onAdd(value.trim());
    toggleForm();
    };
  return (
    <div className={`p-2.5 rounded-sm h-min shadow-sm ${className}`}>
      {showForm ? (
        <Form
          placeholder={placeholder} 
          onSubmit={handleSubmit}
          onClose={toggleForm}
        />
      ) : (
        <AddNew
          onClick={toggleForm} 
          label={label}
        />
      )}
    </div>
  )
}
