import down from '@/assets/icons/arrow-down.svg'
import up from '@/assets/icons/arrow-up.svg'
import TaskTitle from '../ui/Titles/TaskTitle';

interface TaskHeaderProps {
  title: string;
  show: boolean;
  onToggle: () => void;
}

export default function TaskHeader({ title, show , onToggle }: TaskHeaderProps) {
  return (
    <button onClick={onToggle} className="items-center w-full flex justify-between">
        <TaskTitle taskTitle={title} />
        <img className="w-5 h-5" src={show ? down : up} alt="toggle" />
    </button>
  )
}
