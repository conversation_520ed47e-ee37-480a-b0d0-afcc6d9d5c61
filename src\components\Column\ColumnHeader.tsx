import type { Column, Task } from "@/types/board.types";
import ColumnTitle from "../ui/Titles/ColumnTitle";

export default function ColumnHeader( {column , tasks} : {column: Column, tasks: Task[] }) {
  return (
    <div className="flex items-center gap-2.5 mb-2 p-2.5 pb-0">
        <ColumnTitle columnTitle={column.name} />
        <span className="text-base text-gray-500 font-medium">({tasks.length})</span>
    </div>
  )
}
