@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

*{
  box-sizing: border-box;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body, html {
  margin: 0;
  padding: 0;
  font-family: 'Tajawal', sans-serif;
  background-color: var(--bg-primary, #f8fafc);
  color: var(--text-primary, #1e293b);
}

/* تحسين السحب والإفلات للموبايل */
@media (max-width: 768px) {
  [data-rbd-draggable-context-id] {
    touch-action: manipulation;
  }

  [data-rbd-drag-handle-context-id] {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

/* تحسين الانيميشن للسحب */
[data-rbd-draggable-id] {
  transition: transform 0.15s cubic-bezier(0.2, 0, 0, 1);
}

[data-rbd-draggable-id][data-rbd-drag-handle-dragging-id] {
  transition: none !important;
  transform: rotate(2deg) scale(1.05) !important;
  z-index: 9999 !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

/* تحسين منطقة الإسقاط */
[data-rbd-droppable-id] {
  transition: background-color 0.2s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}