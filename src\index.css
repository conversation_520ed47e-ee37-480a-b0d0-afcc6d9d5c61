@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

*{
  box-sizing: border-box;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: '<PERSON>jawal';
  src: url('/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body, html {
  margin: 0;
  padding: 0;
  font-family: 'Tajawal', sans-serif;
  background-color: var(--bg-primary, #f8fafc);
  color: var(--text-primary, #1e293b);
}

/* تحسين السحب والإفلات للموبايل */
@media (max-width: 768px) {
  /* إعدادات اللمس الأساسية */
  [data-rbd-draggable-context-id] {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  [data-rbd-drag-handle-context-id] {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* تحسين التاسك للموبايل */
  [data-rbd-draggable-id] {
    touch-action: none;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    cursor: grab;
    -webkit-tap-highlight-color: transparent;
  }

  /* التاسك أثناء السحب في الموبايل */
  [data-rbd-drag-handle-dragging-id] {
    transform: rotate(3deg) scale(1.1) !important;
    -webkit-transform: rotate(3deg) scale(1.1) translateZ(0) !important;
    z-index: 999999 !important;
    position: fixed !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4) !important;
    opacity: 0.9 !important;
    cursor: grabbing;
  }

  /* تحسين منطقة الإسقاط للموبايل */
  [data-rbd-droppable-id] {
    min-height: 100px;
    padding: 10px;
  }

  /* كلاس مخصص للتاسك أثناء السحب في الموبايل */
  .dragging-task-mobile {
    transform: rotate(3deg) scale(1.1) !important;
    z-index: 999999 !important;
    position: fixed !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4) !important;
    opacity: 0.9 !important;
    transition: none !important;
    pointer-events: none !important;
  }
}

/* تحسين الانيميشن للسحب */
[data-rbd-draggable-id] {
  transition: transform 0.15s cubic-bezier(0.2, 0, 0, 1);
}

/* تحسين السحب والإفلات */
[data-rbd-draggable-id] {
  transition: transform 0.2s cubic-bezier(0.2, 0, 0, 1);
  position: relative;
  z-index: 1;
}

/* التاسك أثناء السحب - أولوية عالية جداً */
[data-rbd-drag-handle-dragging-id] {
  transition: none !important;
  transform: rotate(2deg) scale(1.05) !important;
  z-index: 999999 !important;
  position: fixed !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
  opacity: 0.95 !important;
}

/* الأعمدة لها z-index أقل */
[data-rbd-droppable-id] {
  position: relative;
  z-index: 10;
}

/* تحسين منطقة الإسقاط */
[data-rbd-droppable-id] {
  transition: background-color 0.2s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
}

/* سكرول للعمود */
.column-scroll::-webkit-scrollbar {
  width: 4px;
}

.column-scroll::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.column-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}