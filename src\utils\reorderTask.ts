import type { Column } from "../redux/slice/boardSlice";

export function reorderTask(
  columns: Column[],
  oldColumnId: string,
  newColumnId: string,
  oldIndex: number,
  newIndex: number
): Column[] {
  // نسخ الأعمدة لتفادي التعديل المباشر
  const newColumns = [...columns];

  const sourceColumn = newColumns.find(col => col.id === oldColumnId);
  const destinationColumn = newColumns.find(col => col.id === newColumnId);

  if (!sourceColumn || !destinationColumn) return columns;

  const taskToMove = sourceColumn.tasks[oldIndex];
  if (!taskToMove) return columns;

  // إزالة المهمة من العمود الأصلي
  sourceColumn.tasks.splice(oldIndex, 1);

  // إدخال المهمة في العمود الجديد بالموقع الجديد
  destinationColumn.tasks.splice(newIndex, 0, taskToMove);

  return newColumns;
}
