import { Draggable, Droppable } from "@hello-pangea/dnd";
import Task from "../Task/Task";
import type { Task as TaskType } from "@/types/board.types";

interface Props {
  tasks: TaskType[];
  columnId: string;
}

export default function ColumnTaskList({ tasks, columnId }: Props) {
  return (
    <Droppable droppableId={columnId} type="task">
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          className={`flex flex-col gap-2 flex-1 px-2.5 transition-colors duration-150 ${
            snapshot.isDraggingOver
              ? "bg-blue-50 border-2 border-dashed border-blue-300 rounded-md mx-1"
              : "border-2 border-transparent"
          }`}
        >
          {tasks.map((task, index) => (
            <Draggable draggableId={task.id} index={index} key={task.id}>
              {(provided) => (
                <Task
                  id={task.id}
                  columnId={columnId}
                  description={task.description}
                  title={task.title}
                  dragHandleProps={provided.dragHandleProps}
                  draggableProps={provided.draggableProps}
                  innerRef={provided.innerRef}
                  style={provided.draggableProps.style}
                />
              )}
            </Draggable>
          ))}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
}
