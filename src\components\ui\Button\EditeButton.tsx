import edite from '@/assets/icons/edit.svg'
export interface EditeButtonProps {
    setIsEditing: (value: boolean) => void;
}
export default function EditeButton( {setIsEditing} : EditeButtonProps) {
  return (
    <button className="p-1 hover:bg-gray-100 rounded" onClick={() => setIsEditing(true)}  aria-label="Edit task" >
        <img className="w-4 h-4" src={edite} alt="edit icon" />
    </button>
  )
}
