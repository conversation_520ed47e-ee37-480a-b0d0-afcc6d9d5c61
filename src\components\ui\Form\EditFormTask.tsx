import { useState } from "react";
import Input from "../Input/Input";
import Textarea from "../Textarea/Textarea";
import SubmitButton from "../Button/SubmitButton";
import CloseButton from "../Button/CloseButton";
import { useDispatch } from "react-redux";
import type { EditFormTaskProps } from "@/types/types";
import { editeTask } from "@/redux/slice/boardSlice";

export default function EditFormTask({title , description , columnId , taskId , onClose} : EditFormTaskProps) {
    const [editTitle, setEditTitle] = useState(title);
    const [editDescription, setEditDescription] = useState(description);
    const dispatch = useDispatch();
    const handleSave = (e: React.FormEvent) => {
        e.preventDefault();
        dispatch(editeTask({
        columnId,
        taskId: taskId,
        updatedTask: { title: editTitle.trim(), description: editDescription.trim() }
        }));
        onClose();
    };

    return (
        <form onSubmit={handleSave} className="flex flex-col gap-3">
            <Input value={editTitle} setValue={setEditTitle} placeholder="Task Title" />
            <Textarea editDescription={editDescription} setEditDescription={setEditDescription} />
            <div className="flex gap-2 justify-end">
                <SubmitButton className="bg-blue-400 hover:bg-blue-500 text-white" label="Save" />
                <CloseButton IconColor="invert" onClose={onClose} />
            </div>
        </form>
    )
}
