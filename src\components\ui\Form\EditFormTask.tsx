import { useState } from "react";
import Textarea from "../Textarea/Textarea";
import SubmitButton from "../Button/SubmitButton";
import CloseButton from "../Button/CloseButton";
import { useDispatch } from "react-redux";
import { editeTask } from "@/redux/slice/boardSlice";
export interface EditFormTaskProps {
    title: string;
    description: string;
    columnId: string;
    taskId: string;
    onClose: () => void;
}
export default function EditFormTask({title , description , columnId , taskId , onClose} : EditFormTaskProps) {
    const [editTitle, setEditTitle] = useState(title);
    const [editDescription, setEditDescription] = useState(description);
    const dispatch = useDispatch();
    const handleSave = (e: React.FormEvent) => {
        e.preventDefault();
        dispatch(editeTask({
        columnId,
        taskId: taskId,
        updatedTask: { title: editTitle.trim(), description: editDescription.trim() }
        }));
        onClose();
    };

    return (
        <form onSubmit={handleSave} className="flex flex-col gap-3">
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              placeholder="Task Title"
              className="border border-gray-300 dark:border-slate-600 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-slate-800 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-slate-400 transition-all duration-200"
              autoFocus
            />
            <Textarea editDescription={editDescription} setEditDescription={setEditDescription} />
            <div className="flex gap-2 justify-end">
                <SubmitButton className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white transition-colors duration-200" label="Save" />
                <CloseButton IconColor="invert" onClose={onClose} />
            </div>
        </form>
    )
}
