import { useDispatch } from "react-redux";
import AddItem from "../AddItem/AddItem";
import { useCallback } from "react";
import { v4 as uuidv4 } from 'uuid';
import { addTask } from "@/redux/slice/boardSlice";

interface Props {
  columnId: string;
}
export default function ColumnFooter({columnId}: Props) {
  const dispatch = useDispatch();
  const handleAddTask = useCallback((taskTitle: string) => {
    const newTask = {
      id: uuidv4(),
      title: taskTitle,
      description: '',
    };
    dispatch(addTask({ columnId: columnId, task: newTask }));
  }, [dispatch, columnId]);
  return (
    <div className="p-2.5">
        <AddItem
          placeholder="Add new task"
          label="Add new task"
          onAdd={handleAddTask}
          className="text-white bg-blue-950"
        />
    </div>
  )
}
