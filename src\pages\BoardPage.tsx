import { useSelector, useDispatch } from "react-redux";
import { v4 as uuidv4 } from 'uuid';
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from '@hello-pangea/dnd';
import { handleDragEnd } from '../utils';
import type { RootState } from "@/redux/store/store";
import { addColumn } from "@/redux/slice/boardSlice";
import Header from "@/components/TaskBoardHeader/Header";
import Column from "@/components/Column/Column";
import AddItem from "@/components/AddItem/AddItem";
import { useCallback } from "react";

export default function BoardPage() {
  const columns = useSelector((state: RootState) => state.boards.columns);
  const dispatch = useDispatch();

  const handleAddColumn = useCallback((columnName: string) => {
    const newId = uuidv4();
    dispatch(addColumn({ id: newId, name: columnName }));
  }, [dispatch]);

  return (
    <section className="bg-gradient-to-b from-blue-900 via-blue-700 to-blue-500 min-h-screen h-screen flex flex-col overflow-hidden">
      <Header />
      <DragDropContext
        onDragEnd={(result: DropResult) => handleDragEnd(result, columns, dispatch)}
      >
        <main className="flex flex-row sm:flex-row min-h-screen sm:h-screen px-5 sm:px-10 py-5 overflow-auto">
          <Droppable droppableId="board" direction="horizontal" type="column">
            {(provided) => (
              <div
                className="flex gap-2.5 min-w-fit h-full"
                ref={provided.innerRef}
                {...provided.droppableProps}
              >
                {columns.map((column, index) => (
                  <Draggable draggableId={column.id} index={index} key={column.id}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <Column column={column} />
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
                <div className="w-[300px]">
                  <AddItem
                    placeholder="Add new column"
                    label="Add new column"
                    onAdd={handleAddColumn}
                    className="text-white bg-white/20"
                  />
                </div>
              </div>
            )}
          </Droppable>
        </main>
      </DragDropContext>
    </section>
  );
}
